{"questions": [{"question_number": 11, "Question": "कथन :\nP < E < T ≤ R; T ≥ K\nनिष्कर्ष :\nI. K > P\nII. R ≥ K", "option1": "उत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है", "option2": "उत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है", "option3": "उत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है", "option4": "उत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं", "option5": "उत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "explanation": "(5) P < E < T ≤ R; T ≥ K\nनिष्कर्ष:\nI. K > P : असत्य\nII. R ≥ K : सत्य", "correctAnswer": "5", "directions": "निर्देश (11-15) : इन प्रश्नों में, विभिन्न तत्वों के बीच संबंध कथनों में दर्शाया गया है। इन कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। कथनों पर आधारित निष्कर्षों को पढ़िए तथा उपयुक्त उत्तर का चयन कीजिए :\n\nउत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है\nउत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है\nउत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है\nउत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं\nउत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 12, "Question": "कथन :\nX < W; A > C ≥ H = W\nनिष्कर्ष :\nI. C > X\nII. A > W", "option1": "उत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है", "option2": "उत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है", "option3": "उत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है", "option4": "उत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं", "option5": "उत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "explanation": "(4) X < W; A > C ≥ H = W\nA > C ≥ H = W > X\nनिष्कर्ष:\nI. C > X : सत्य\nII. A > W : सत्य", "correctAnswer": "4", "directions": "निर्देश (11-15) : इन प्रश्नों में, विभिन्न तत्वों के बीच संबंध कथनों में दर्शाया गया है। इन कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। कथनों पर आधारित निष्कर्षों को पढ़िए तथा उपयुक्त उत्तर का चयन कीजिए :\n\nउत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है\nउत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है\nउत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है\nउत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं\nउत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 13, "Question": "कथन :\nJ ≤ L < B ≤ S > Y < M\nनिष्कर्ष :\nI. J < M\nII. L ≥ Y", "option1": "उत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है", "option2": "उत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है", "option3": "उत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है", "option4": "उत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं", "option5": "उत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "explanation": "(2) J ≤ L < B ≤ S > Y < M\nनिष्कर्ष:\nI. J < M : असत्य\nII. L ≥ Y : असत्य", "correctAnswer": "2", "directions": "निर्देश (11-15) : इन प्रश्नों में, विभिन्न तत्वों के बीच संबंध कथनों में दर्शाया गया है। इन कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। कथनों पर आधारित निष्कर्षों को पढ़िए तथा उपयुक्त उत्तर का चयन कीजिए :\n\nउत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है\nउत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है\nउत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है\nउत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं\nउत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 14, "Question": "कथन :\nB ≤ N < D; K = R < D ≤ W\nनिष्कर्ष :\nI. N = R\nII. B < W", "option1": "उत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है", "option2": "उत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है", "option3": "उत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है", "option4": "उत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं", "option5": "उत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "explanation": "(5) B ≤ N < D; K = R < D ≤ W\nनिष्कर्ष:\nI. N = R : असत्य\nII. B < W : सत्य", "correctAnswer": "5", "directions": "निर्देश (11-15) : इन प्रश्नों में, विभिन्न तत्वों के बीच संबंध कथनों में दर्शाया गया है। इन कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। कथनों पर आधारित निष्कर्षों को पढ़िए तथा उपयुक्त उत्तर का चयन कीजिए :\n\nउत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है\nउत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है\nउत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है\nउत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं\nउत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 15, "Question": "कथन :\nB ≤ N < D; K = R < D ≤ W\nनिष्कर्ष :\nI. W > K\nII. N ≥ W", "option1": "उत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है", "option2": "उत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है", "option3": "उत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है", "option4": "उत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं", "option5": "उत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "explanation": "(3) B ≤ N < D; K = R < D ≤ W\nनिष्कर्ष:\nI. W > K : सत्य\nII. N ≥ W : असत्य", "correctAnswer": "3", "directions": "निर्देश (11-15) : इन प्रश्नों में, विभिन्न तत्वों के बीच संबंध कथनों में दर्शाया गया है। इन कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। कथनों पर आधारित निष्कर्षों को पढ़िए तथा उपयुक्त उत्तर का चयन कीजिए :\n\nउत्तर (1) दीजिए यदि या तो निष्कर्ष I या निष्कर्ष II सत्य है\nउत्तर (2) दीजिए यदि न तो निष्कर्ष I न ही निष्कर्ष II सत्य है\nउत्तर (3) दीजिए यदि केवल निष्कर्ष I सत्य है\nउत्तर (4) दीजिए यदि निष्कर्ष I और II दोनों सत्य हैं\nउत्तर (5) दीजिए यदि केवल निष्कर्ष II सत्य है", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 16, "Question": "आठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।\n\nM के ठीक बाएँ कौन है?", "option1": "N", "option2": "B", "option3": "D", "option4": "P", "option5": "C", "explanation": "(5) M के ठीक बाएँ C है।", "correctAnswer": "5", "directions": "निर्देश (16-20) : निम्नलिखित जानकारी का सावधानीपूर्वक अध्ययन कर नीचे दिए गए प्रश्नों के उत्तर दीजिए :\nआठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 17, "Question": "P के संदर्भ में O का स्थान क्या है?", "option1": "बाएँ चौथा", "option2": "बाएँ तीसरा", "option3": "दाएँ तीसरा", "option4": "ठीक दाएँ", "option5": "ठीक बाएँ", "explanation": "(2) P के बाएँ तीसरे स्थान पर O है।", "correctAnswer": "2", "directions": "निर्देश (16-20) : निम्नलिखित जानकारी का सावधानीपूर्वक अध्ययन कर नीचे दिए गए प्रश्नों के उत्तर दीजिए :\nआठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 18, "Question": "दी गई व्यवस्था के अनुसार निम्नलिखित में से कौन-सा कथन सत्य है?", "option1": "दिए गए कथनों में से कोई भी सत्य नहीं है", "option2": "O तथा D के बीच केवल तीन व्यक्ति बैठे हैं", "option3": "A के बाएँ तीसरे स्थान पर D बैठा है", "option4": "मेज के किसी एक कोने पर M बैठा है", "option5": "<PERSON>, <PERSON> का एक निकटतम पड़ोसी है", "explanation": "(1) D के ठीक बाएँ O है।\nA के दाएँ दूसरे स्थान पर D है।\nकिसी किनारे के मध्य में M बैठा है।\nM निकटतम पड़ोसी है N तथा C दोनों का।\nउपरोक्त में से कोई भी विकल्प सत्य नहीं है।", "correctAnswer": "1", "directions": "निर्देश (16-20) : निम्नलिखित जानकारी का सावधानीपूर्वक अध्ययन कर नीचे दिए गए प्रश्नों के उत्तर दीजिए :\nआठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 19, "Question": "दी गई व्यवस्था के अनुसार निम्नलिखित पाँच में से चार एक निश्चित प्रकार से एक समान हैं तथा वे अपना एक समूह बनाते हैं। निम्नलिखित में से कौन-सा एक है जो इस समूह में शामिल नहीं होता है?", "option1": "D", "option2": "A", "option3": "P", "option4": "O", "option5": "M", "explanation": "(4) O को छोड़कर, अन्य सभी किनारों के मध्य में बैठे हैं।", "correctAnswer": "4", "directions": "निर्देश (16-20) : निम्नलिखित जानकारी का सावधानीपूर्वक अध्ययन कर नीचे दिए गए प्रश्नों के उत्तर दीजिए :\nआठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 20, "Question": "यदि B के बायाँ और बढ़ा जाए तो B तथा N के ठीक दाएँ बैठे व्यक्ति के ठीक बीच में निम्नलिखित में से कौन बैठे हैं?", "option1": "<PERSON>, <PERSON>", "option2": "<PERSON>, <PERSON>", "option3": "C, P", "option4": "<PERSON>, <PERSON>", "option5": "A, C", "explanation": "(3) N के ठीक दाएँ M बैठा है।\nयदि B के बायाँ और बढ़ा जाए तो B तथा M के बीच दो व्यक्ति P तथा C बैठे हैं।", "correctAnswer": "3", "directions": "निर्देश (16-20) : निम्नलिखित जानकारी का सावधानीपूर्वक अध्ययन कर नीचे दिए गए प्रश्नों के उत्तर दीजिए :\nआठ व्यक्ति - A, B, C, D, M, N, O तथा P - एक वर्गाकार मेज के गिर्द इस प्रकार बैठे हैं (परंतु आवश्यक नहीं है कि इसी क्रम में) कि उनमें से चार मेज के चार कोनों पर जबकि अन्य चार मेज के चारों किनारों के मध्य में बैठे हैं। किनारों के मध्य में बैठे व्यक्तियों का मुख केन्द्र की ओर है तथा जो कोनों पर बैठे हैं उनका मुख बाहर की ओर है (अर्थात् केन्द्र से विपरीत दिशा की ओर)।\nकिसी किनारे के मध्य में A बैठा है। A तथा M के बीच केवल एक व्यक्ति बैठा है। B के दाएँ तीसरे स्थान पर A बैठा है।\nB तथा N के बीच केवल तीन व्यक्ति बैठे हैं। N के दाएँ दूसरे स्थान पर C बैठा है। O तथा C का मुख एक दिशा की ओर है। M, O का एक निकटतम पड़ोसी नहीं है। D के दाएँ दूसरे स्थान पर P बैठा है।", "question_images": [], "option_images": [], "explanation_images": []}]}